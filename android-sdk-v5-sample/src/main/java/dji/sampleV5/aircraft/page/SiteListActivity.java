package dji.sampleV5.aircraft.page;

import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.GridLayoutManager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gyf.immersionbar.BarHide;
import com.gyf.immersionbar.ImmersionBar;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivitySitelistBinding;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.event.Message;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.ext.DialogExtKt;
import dji.sampleV5.aircraft.mvvm.key.ValueKey;
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.net.bean.qicloud.ListItem;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.utils.common.LogUtils;
import io.socket.client.IO;
import io.socket.client.Socket;
import io.socket.emitter.Emitter;
import me.jessyan.autosize.internal.CancelAdapt;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SiteListActivity extends AppCompatActivity implements CancelAdapt {
    private static final String TAG = SiteListActivity.class.getName();
    /**
     * 循环任务周期
     */
    private static final int INTERVAL_DELAY = 5000;
    /**
     * 最大无响应时间
     */
    private static final int MAX_NO_RESPONSE_TIME = 30000;

    private TaskAdapter adapter;
    private Map<String, ListItemWrap> itemsMap;
    private Socket mSocket;
    private Timer mTimer;
    private TimerTask mTimerTask;
    private ActivitySitelistBinding binding;

    /**
     * 接受服务器端数据
     */
    private Callback<ResponseBody> createDateHandler() {
        return new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                try {
                    JSONObject resp = JSONObject.parseObject(response.body().string());
                    int code = resp.getInteger("code");
                    if (code != 0) {
                        ToastUtil.show("加载数据失败");
                        return;
                    }

                    JSONObject data = resp.getJSONObject("data");
                    if (data == null) {
                        ToastUtil.show("加载数据失败");
                        return;
                    }

                    JSONArray list = data.getJSONArray("list");
                    if (list == null) {
                        ToastUtil.show("加载数据失败");
                        return;
                    }

                    SiteListActivity.this.onListItems(list);
                } catch (NullPointerException | IOException e) {
                    e.printStackTrace();
                    ToastUtil.show("加载数据失败");
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                XLogUtil.INSTANCE.e(TAG, "onFailure:" + t.toString());
                ToastUtil.show("加载数据失败");
            }
        };
    }

    /**
     * 监听所有站点的变化
     */
    private Emitter.Listener createSiteChangeListener() {
        return new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                try {
                    org.json.JSONObject json = (org.json.JSONObject) args[0];
                    String STID = json.getString("STID");
                    ListItemWrap target = itemsMap.get(STID);
                    if (target == null) {
                        return;
                    }
                    target.call(args);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        if (RealTimeActivity.Companion.isCreate()) {
                            LiveDataEvent.INSTANCE.getSocketData().postValue((org.json.JSONObject) args[0]);
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        };
    }

    private void setupSocket(LoginCache loginCache) {
        LoginCache.SocketIOConfig socketIOConfig = loginCache.getSocketIOConfig();
        if (socketIOConfig == null) {
            ToastUtil.show("远程连接创建失败");
            return;
        }
        try {
            String query = String.format("accessKeyId=%s&accessKeySecret=%s", socketIOConfig.accessKeyId, socketIOConfig.accessKeySecret);
            String address = String.format("%s/user", socketIOConfig.IPAddress);
            if(!NetConfig.isSocketHttps){
                address = String.format("%s/user", socketIOConfig.IPAddress);
            }
            XLogUtil.INSTANCE.e(TAG, String.format("setupSocket query:%s address:%s", query, address));

            IO.Options opts = IO.Options.builder()
                    .setQuery(query)
                    .setReconnection(false)
                    .setTransports(new String[]{"websocket"})
                    .build();
            // 确保 address 包含 https:// 前缀
            String modifiedAddress = ensureHttpsPrefix(address);
            mSocket = IO.socket(modifiedAddress, opts);
            mSocket.on(Socket.EVENT_CONNECT_ERROR, new Emitter.Listener() {
                @Override
                public void call(Object... args) {
                    XLogUtil.INSTANCE.e(TAG, String.format("EVENT_CONNECT_ERROR %s", args[0]));
                }
            });

            mSocket.on(Socket.EVENT_CONNECT, new Emitter.Listener() {
                @Override
                public void call(Object... args) {
                    XLogUtil.INSTANCE.e(TAG, "socket.connect");
                }
            });
            mSocket.on("site state", createSiteChangeListener());
            mSocket.on("user login", new Emitter.Listener() {
                @Override
                public void call(Object... args) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtil.show("账号在其他设备登录");
                            XLogUtil.INSTANCE.e(TAG, "socket.user login");
                        }
                    });
                }
            });

            mSocket.connect();
        } catch (URISyntaxException e) {
            ToastUtil.show("远程连接创建失败");
            e.printStackTrace();
        }
    }

    void onInterval() {
        this.mTimer.schedule(this.mTimerTask, INTERVAL_DELAY);
    }

    private static String ensureHttpsPrefix(String address) {
        if (address.startsWith("https://") || address.startsWith("http://")) {
            return address;
        } else {
            if (NetConfig.isSocketHttps) {
                return "https://" + address;
            } else {
                return "http://" + address;
            }
        }
    }

    /**
     * 成功获得到列表数据后, 绑定 socket 监听
     */
    private void onListItems(JSONArray list) {
        List<ListItem> parsed = list.toJavaList(ListItem.class);
        XLogUtil.INSTANCE.e(TAG, String.format("onListItems parsed:%d", parsed.size()));

        List<ListItemWrap> next = new ArrayList<>();
        Map<String, ListItemWrap> nextMap = new HashMap<>();
        for (ListItem item : parsed) {
            XLogUtil.INSTANCE.e(TAG, String.format("onListItems each:%s, item:%s", next.size(), JSONObject.toJSONString(item)));
            ListItemWrap wrap = new ListItemWrap(this, adapter, item, next.size());
            //System.out.println(">>" + i.siteName); // todo 解除注释方便调试
            // 智慧魔方,临港M210
            //if ("智慧魔方".equals(i.siteName)) {
            //    next.add(wrap);
            //}
            next.add(wrap);
            nextMap.put(item.siteID, wrap);

            String eventName = item.siteID + " state";
            mSocket.on(eventName, createSiteChangeListener());
        }

        // 切换列表
        this.itemsMap = nextMap;

        // adapter 接受新数据
        adapter.loadData(next);
    }

    /**
     * item 点击时调用
     */
    private void onItemClick(View view) {
        /*ListItemWrap wrap = (ListItemWrap) view.getTag();
        if (wrap == null) {
            return;
        }
        Log.e(TAG, String.format("onItemClick siteID: %s", wrap.getItem().siteID));
        ToastUtil.show(String.format("[%s]被点击了", wrap.getItem().siteID));*/
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
//        getWindow().setStatusBarColor(getResources().getColor(android.R.color.transparent));
        super.onCreate(savedInstanceState);

        // 机场项目
        itemsMap = new HashMap<>();

        // ui binding
        binding = DataBindingUtil.setContentView(this, R.layout.activity_sitelist);
        binding.ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        LoginCache loginCache = SpUtil.getLoginCache();
        XLogUtil.INSTANCE.e(TAG, "onCreate:" + loginCache.getQicloudToken());

        // 创建 socket 连接
        setupSocket(loginCache);

        // 创建 adapter
        adapter = new TaskAdapter(this::onItemClick);

        // 修饰 recycleView
        int o = getResources().getConfiguration().orientation;
        int dOri = o == Configuration.ORIENTATION_LANDSCAPE ? DividerItemDecoration.HORIZONTAL : DividerItemDecoration.VERTICAL;
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.recyclerView.getContext(), dOri);
        binding.recyclerView.setLayoutManager(new GridLayoutManager(this, 2));
        binding.recyclerView.setAdapter(adapter);
        // binding.recyclerView.addItemDecoration(dividerItemDecoration);

        // 拉取站点列表
       /* QiCloud.init(loginCache);
        QiCloud.getQcpSitesList().enqueue(createDateHandler());*/
        getSiteList();

        // 定时器存在的意义是查看, 如果有一个站点 30s 没有监听到任何事件, 标记此站点为离线
        this.mTimer = new Timer();
        this.mTimerTask = new TimerTask() {
            @Override
            public void run() {
                long earliest = new Date().getTime() - MAX_NO_RESPONSE_TIME;
                for (ListItemWrap i : itemsMap.values()) {
                    i.checkOffline(earliest);
                }
            }
        };

        // 启动定时器
        onInterval();
    }

    private void getSiteList(){
        LoginCache loginCache = SpUtil.getLoginCache();
        JSONObject data = new JSONObject();
        data.put("page", 1);
        data.put("size", 100);
        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.create(JSON, data.toJSONString());
        String url = NetConfig.MIXURL + "site/list";
        Request request = new Request.Builder().
                url(url).
                addHeader("ProductId",NetConfig.PID).
                addHeader("ApiToken",loginCache.getApiToken()).
                post(requestBody)
                .build();
        XLogUtil.INSTANCE.d("TAG", "login getSiteList: " + url + "    request:" + request + "    data:" + data.toJSONString());
        okhttp3.Call call = httpClient.newCall(request);
        DialogExtKt.showLoadingExt(SiteListActivity.this, "正在加载站点数据", null);
        call.enqueue(new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                DialogExtKt.dismissLoadingExt(SiteListActivity.this);
                ToastUtil.show("尝试加载离线数据");
                loadSiteList(SpUtil.getSiteList());
                XLogUtil.INSTANCE.e("TAG", "login onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());

            }

            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                DialogExtKt.dismissLoadingExt(SiteListActivity.this);
                //在这里根据返回内容执行具体的操作
                //Log.e("TAG", "login onResponse: " + response.body().string());
                String result = response.body().string();
                XLogUtil.INSTANCE.e("TAG", "login onResponse: " + result);
                SpUtil.setSiteList(result);
                loadSiteList(result);
            }
        });
    }

    private void loadSiteList(String result){
        try {

            JSONObject jsonObject = JSONObject.parseObject(result);
            if(jsonObject == null){
                return;
            }
            if (TextUtils.equals(jsonObject.getString("message"), "success")) {
                JSONArray list = jsonObject.getJSONArray("data");
                if (list == null) {
                    ToastUtil.show("加载数据失败");
                    return;
                }

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        SiteListActivity.this.onListItems(list);
                    }
                });
            } else {
                ToastUtil.show(jsonObject.getString("message"));
            }
        } catch (com.alibaba.fastjson.JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onBackPressed() {
        finish();
    }

    @Override
    protected void onResume() {
        super.onResume();
        ImmersionBar.with(this).hideBar(BarHide.FLAG_HIDE_STATUS_BAR).navigationBarColor(R.color.transparent).init();
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();

        // 结束 webSocket
        if (this.mSocket != null) {
            this.mSocket.disconnect();
            this.mSocket = null;
        }

        // 结束定时器
        this.mTimer.cancel();
    }
}
