package dji.sampleV5.aircraft.mvvm.manager;

import android.text.TextUtils;

import com.tencent.mmkv.MMKV;

import java.util.List;

import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.mvvm.ext.StorageExtKt;
import dji.sampleV5.aircraft.mvvm.key.ValueKey;
import dji.sampleV5.aircraft.mvvm.net.response.SiteInfoBean;
import dji.sampleV5.aircraft.mvvm.net.response.SiteListBean;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.util.JsonUtil;

/**
 * 站点信息缓存管理器
 * 统一管理站点信息的缓存策略，支持MMKV和SpUtil双重缓存
 * 
 * <AUTHOR> (Engineer)
 * @version 1.0
 * @date 2025-01-25
 */
public class SiteInfoCacheManager {
    
    private static final String TAG = "SiteInfoCacheManager";
    
    // 缓存过期时间：24小时
    private static final long CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000L;
    
    // 缓存键名
    private static final String SITE_INFO_CACHE_KEY = "site_info_cache_v1";
    private static final String SITE_LIST_CACHE_KEY = "site_list_cache_v1";
    private static final String CACHE_TIMESTAMP_KEY = "site_cache_timestamp";
    
    // 单例实例
    private static volatile SiteInfoCacheManager instance;
    private final MMKV mmkv;
    
    private SiteInfoCacheManager() {
        mmkv = StorageExtKt.getMmkv();
    }
    
    /**
     * 获取单例实例
     */
    public static SiteInfoCacheManager getInstance() {
        if (instance == null) {
            synchronized (SiteInfoCacheManager.class) {
                if (instance == null) {
                    instance = new SiteInfoCacheManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 保存站点信息到缓存
     * @param siteInfo 站点信息
     */
    public void saveSiteInfo(SiteInfoBean siteInfo) {
        if (siteInfo == null) {
            XLogUtil.INSTANCE.w(TAG, "尝试保存空的站点信息，跳过");
            return;
        }
        
        try {
            long currentTime = System.currentTimeMillis();
            String siteInfoJson = JsonUtil.toJson(siteInfo);
            
            // 主缓存：MMKV
            mmkv.putString(SITE_INFO_CACHE_KEY, siteInfoJson);
            mmkv.putLong(CACHE_TIMESTAMP_KEY, currentTime);
            
            // 备份缓存：SpUtil (兼容现有代码)
            mmkv.putString(ValueKey.SITE_INFO, siteInfoJson);
            
            XLogUtil.INSTANCE.d(TAG, "站点信息已保存到缓存 - 站点ID: " + siteInfo.getSiteId());
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "保存站点信息到缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 从缓存获取站点信息
     * @return 站点信息，如果不存在或已过期则返回null
     */
    public SiteInfoBean getSiteInfo() {
        try {
            // 优先从MMKV主缓存读取
            String cacheJson = mmkv.getString(SITE_INFO_CACHE_KEY, null);
            long cacheTimestamp = mmkv.getLong(CACHE_TIMESTAMP_KEY, 0);
            
            if (!TextUtils.isEmpty(cacheJson) && isCacheValid(cacheTimestamp)) {
                SiteInfoBean siteInfo = JsonUtil.fromJson(cacheJson, SiteInfoBean.class);
                if (siteInfo != null) {
                    XLogUtil.INSTANCE.d(TAG, "从MMKV缓存获取站点信息成功 - 站点ID: " + siteInfo.getSiteId());
                    return siteInfo;
                }
            }
            
            // 降级到备份缓存
            String backupJson = mmkv.getString(ValueKey.SITE_INFO, null);
            if (!TextUtils.isEmpty(backupJson)) {
                SiteInfoBean siteInfo = JsonUtil.fromJson(backupJson, SiteInfoBean.class);
                if (siteInfo != null) {
                    XLogUtil.INSTANCE.d(TAG, "从备份缓存获取站点信息成功 - 站点ID: " + siteInfo.getSiteId());
                    // 同步到主缓存
                    saveSiteInfo(siteInfo);
                    return siteInfo;
                }
            }
            
            XLogUtil.INSTANCE.d(TAG, "缓存中未找到有效的站点信息");
            return null;
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "从缓存获取站点信息失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 保存站点列表到缓存
     * @param siteList 站点列表
     */
    public void saveSiteList(List<SiteListBean.SiteItem> siteList) {
        if (siteList == null || siteList.isEmpty()) {
            XLogUtil.INSTANCE.w(TAG, "尝试保存空的站点列表，跳过");
            return;
        }
        
        try {
            long currentTime = System.currentTimeMillis();
            String siteListJson = JsonUtil.toJson(siteList);
            
            // 主缓存：MMKV
            mmkv.putString(SITE_LIST_CACHE_KEY, siteListJson);
            mmkv.putLong(CACHE_TIMESTAMP_KEY, currentTime);
            
            // 备份缓存：SpUtil
            SpUtil.setSiteList(siteListJson);
            
            XLogUtil.INSTANCE.d(TAG, "站点列表已保存到缓存 - 数量: " + siteList.size());
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "保存站点列表到缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 从缓存获取站点列表
     * @return 站点列表，如果不存在或已过期则返回null
     */
    public List<SiteListBean.SiteItem> getSiteList() {
        try {
            // 优先从MMKV主缓存读取
            String cacheJson = mmkv.getString(SITE_LIST_CACHE_KEY, null);
            long cacheTimestamp = mmkv.getLong(CACHE_TIMESTAMP_KEY, 0);
            
            if (!TextUtils.isEmpty(cacheJson) && isCacheValid(cacheTimestamp)) {
                List<SiteListBean.SiteItem> siteList = JsonUtil.fromJsonArray(cacheJson, SiteListBean.SiteItem.class);
                if (siteList != null && !siteList.isEmpty()) {
                    XLogUtil.INSTANCE.d(TAG, "从MMKV缓存获取站点列表成功 - 数量: " + siteList.size());
                    return siteList;
                }
            }
            
            // 降级到备份缓存
            String backupJson = SpUtil.getSiteList();
            if (!TextUtils.isEmpty(backupJson)) {
                List<SiteListBean.SiteItem> siteList = JsonUtil.fromJsonArray(backupJson, SiteListBean.SiteItem.class);
                if (siteList != null && !siteList.isEmpty()) {
                    XLogUtil.INSTANCE.d(TAG, "从备份缓存获取站点列表成功 - 数量: " + siteList.size());
                    // 同步到主缓存
                    saveSiteList(siteList);
                    return siteList;
                }
            }
            
            XLogUtil.INSTANCE.d(TAG, "缓存中未找到有效的站点列表");
            return null;
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "从缓存获取站点列表失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查缓存是否有效（未过期）
     * @param timestamp 缓存时间戳
     * @return true表示缓存有效，false表示已过期
     */
    public boolean isCacheValid(long timestamp) {
        if (timestamp <= 0) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        boolean isValid = (currentTime - timestamp) < CACHE_EXPIRE_TIME;
        
        if (!isValid) {
            XLogUtil.INSTANCE.d(TAG, "缓存已过期 - 缓存时间: " + timestamp + ", 当前时间: " + currentTime);
        }
        
        return isValid;
    }
    
    /**
     * 检查当前缓存是否有效
     * @return true表示缓存有效，false表示已过期或不存在
     */
    public boolean isCacheValid() {
        long cacheTimestamp = mmkv.getLong(CACHE_TIMESTAMP_KEY, 0);
        return isCacheValid(cacheTimestamp);
    }
    
    /**
     * 清除所有站点相关缓存
     */
    public void clearCache() {
        try {
            // 清除MMKV缓存
            mmkv.remove(SITE_INFO_CACHE_KEY);
            mmkv.remove(SITE_LIST_CACHE_KEY);
            mmkv.remove(CACHE_TIMESTAMP_KEY);
            mmkv.remove(ValueKey.SITE_INFO);
            mmkv.remove(ValueKey.SITE_ID);
            mmkv.remove(ValueKey.UAV_ID);
            mmkv.remove(ValueKey.HIVE_ID);
            
            // 清除SpUtil缓存
            SpUtil.setSiteList("");
            
            XLogUtil.INSTANCE.d(TAG, "所有站点缓存已清除");
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "清除站点缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除用户特定的站点缓存（退出登录时使用）
     * 保留基础站点列表，清除用户绑定的站点信息
     */
    public void clearUserSpecificCache() {
        try {
            // 清除用户特定的站点信息
            mmkv.remove(SITE_INFO_CACHE_KEY);
            mmkv.remove(ValueKey.SITE_INFO);
            mmkv.remove(ValueKey.SITE_ID);
            mmkv.remove(ValueKey.UAV_ID);
            mmkv.remove(ValueKey.HIVE_ID);
            
            // 保留站点列表缓存，因为这是公共数据
            // mmkv.remove(SITE_LIST_CACHE_KEY); // 不清除
            
            XLogUtil.INSTANCE.d(TAG, "用户特定的站点缓存已清除");
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "清除用户特定站点缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 同步缓存到SpUtil（兼容性方法）
     */
    public void syncCacheToSpUtil() {
        try {
            SiteInfoBean siteInfo = getSiteInfo();
            if (siteInfo != null) {
                mmkv.putString(ValueKey.SITE_INFO, JsonUtil.toJson(siteInfo));
            }
            
            List<SiteListBean.SiteItem> siteList = getSiteList();
            if (siteList != null && !siteList.isEmpty()) {
                SpUtil.setSiteList(JsonUtil.toJson(siteList));
            }
            
            XLogUtil.INSTANCE.d(TAG, "缓存已同步到SpUtil");
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "同步缓存到SpUtil失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取缓存状态信息（用于调试）
     * @return 缓存状态描述
     */
    public String getCacheStatus() {
        try {
            long timestamp = mmkv.getLong(CACHE_TIMESTAMP_KEY, 0);
            boolean hasMainCache = !TextUtils.isEmpty(mmkv.getString(SITE_INFO_CACHE_KEY, null));
            boolean hasBackupCache = !TextUtils.isEmpty(mmkv.getString(ValueKey.SITE_INFO, null));
            boolean isValid = isCacheValid(timestamp);
            
            return String.format("缓存状态 - 主缓存: %s, 备份缓存: %s, 有效性: %s, 时间戳: %d", 
                hasMainCache ? "存在" : "不存在",
                hasBackupCache ? "存在" : "不存在", 
                isValid ? "有效" : "无效",
                timestamp);
                
        } catch (Exception e) {
            return "获取缓存状态失败: " + e.getMessage();
        }
    }
}
