package dji.sampleV5.aircraft.mvvm.manager;

import android.text.TextUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.mvvm.net.repository.UserRepository;
import dji.sampleV5.aircraft.mvvm.net.response.SiteApiPageResponse;
import dji.sampleV5.aircraft.mvvm.net.response.SiteInfoBean;
import dji.sampleV5.aircraft.mvvm.net.response.SiteListBean;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.page.login.LoginCache;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * 站点信息初始化器
 * 负责在用户登录成功后主动获取和初始化站点信息
 * 
 * <AUTHOR> (Engineer)
 * @version 1.0
 * @date 2025-01-25
 */
public class SiteInfoInitializer {
    
    private static final String TAG = "SiteInfoInitializer";
    
    // 网络请求超时时间
    private static final int NETWORK_TIMEOUT_SECONDS = 10;
    
    // 单例实例
    private static volatile SiteInfoInitializer instance;
    
    private final SiteInfoCacheManager cacheManager;
    private final SiteInfoValidator validator;
    private final UserRepository userRepository;
    
    private SiteInfoInitializer() {
        cacheManager = SiteInfoCacheManager.getInstance();
        validator = SiteInfoValidator.getInstance();
        userRepository = new UserRepository();
    }
    
    /**
     * 获取单例实例
     */
    public static SiteInfoInitializer getInstance() {
        if (instance == null) {
            synchronized (SiteInfoInitializer.class) {
                if (instance == null) {
                    instance = new SiteInfoInitializer();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化站点信息（登录成功后调用）
     * @param loginCache 登录缓存信息
     * @return Observable<SiteInfoResult> 初始化结果
     */
    public Observable<SiteInfoResult> initializeSiteInfo(LoginCache loginCache) {
        if (loginCache == null) {
            XLogUtil.INSTANCE.e(TAG, "登录缓存为空，无法初始化站点信息");
            return Observable.just(SiteInfoResult.error("登录信息无效"));
        }
        
        XLogUtil.INSTANCE.d(TAG, "开始初始化站点信息 - 用户: " + loginCache.getUserName());
        
        return Observable.fromCallable(() -> {
            // 1. 检查本地缓存
            SiteInfoBean cachedSiteInfo = cacheManager.getSiteInfo();
            if (cachedSiteInfo != null && validator.validateSiteInfo(cachedSiteInfo)) {
                XLogUtil.INSTANCE.d(TAG, "使用本地缓存的站点信息 - 站点ID: " + cachedSiteInfo.getSiteId());
                return SiteInfoResult.success(cachedSiteInfo, SiteInfoSource.CACHE);
            }
            
            // 2. 缓存无效，从网络获取
            XLogUtil.INSTANCE.d(TAG, "本地缓存无效，开始从网络获取站点信息");
            return fetchSiteInfoFromNetwork();
            
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread())
          .timeout(NETWORK_TIMEOUT_SECONDS, TimeUnit.SECONDS)
          .onErrorReturn(throwable -> {
              XLogUtil.INSTANCE.e(TAG, "站点信息初始化失败: " + throwable.getMessage());
              
              // 网络失败时尝试使用缓存
              SiteInfoBean fallbackSiteInfo = cacheManager.getSiteInfo();
              if (fallbackSiteInfo != null) {
                  XLogUtil.INSTANCE.d(TAG, "网络失败，使用降级缓存");
                  return SiteInfoResult.success(fallbackSiteInfo, SiteInfoSource.FALLBACK_CACHE);
              }
              
              return SiteInfoResult.error("站点信息获取失败: " + throwable.getMessage());
          });
    }
    
    /**
     * 从网络获取站点信息
     * @return SiteInfoResult 获取结果
     */
    private SiteInfoResult fetchSiteInfoFromNetwork() {
        try {
            // 首先尝试获取站点列表
            SiteApiPageResponse<SiteListBean.SiteItem> siteListResponse = 
                userRepository.getMultisiteListInfo().await();
                
            if (siteListResponse != null && siteListResponse.getData() != null && 
                !siteListResponse.getData().isEmpty()) {
                
                List<SiteListBean.SiteItem> siteList = siteListResponse.getData();
                XLogUtil.INSTANCE.d(TAG, "从网络获取站点列表成功 - 数量: " + siteList.size());
                
                // 保存站点列表到缓存
                cacheManager.saveSiteList(siteList);
                
                // 获取第一个站点的详细信息
                SiteListBean.SiteItem firstSite = siteList.get(0);
                if (!TextUtils.isEmpty(firstSite.getSiteId())) {
                    
                    SiteInfoBean siteInfo = userRepository.getSiteInfo(firstSite.getSiteId()).await();
                    if (siteInfo != null && validator.validateSiteInfo(siteInfo)) {
                        
                        // 保存站点信息到缓存
                        cacheManager.saveSiteInfo(siteInfo);
                        
                        XLogUtil.INSTANCE.d(TAG, "从网络获取站点信息成功 - 站点ID: " + siteInfo.getSiteId());
                        return SiteInfoResult.success(siteInfo, SiteInfoSource.NETWORK);
                    }
                }
            }
            
            XLogUtil.INSTANCE.w(TAG, "网络获取站点信息失败 - 响应数据无效");
            return SiteInfoResult.error("网络响应数据无效");
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "网络获取站点信息异常: " + e.getMessage());
            return SiteInfoResult.error("网络请求异常: " + e.getMessage());
        }
    }
    
    /**
     * 刷新站点信息（手动刷新时调用）
     * @return Observable<SiteInfoResult> 刷新结果
     */
    public Observable<SiteInfoResult> refreshSiteInfo() {
        XLogUtil.INSTANCE.d(TAG, "开始手动刷新站点信息");
        
        return Observable.fromCallable(this::fetchSiteInfoFromNetwork)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .timeout(NETWORK_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .onErrorReturn(throwable -> {
                XLogUtil.INSTANCE.e(TAG, "手动刷新站点信息失败: " + throwable.getMessage());
                return SiteInfoResult.error("刷新失败: " + throwable.getMessage());
            });
    }
    
    /**
     * 检查站点信息可用性
     * @return SiteInfoResult 检查结果
     */
    public SiteInfoResult checkSiteInfoAvailability() {
        try {
            SiteInfoBean siteInfo = cacheManager.getSiteInfo();
            
            if (siteInfo == null) {
                XLogUtil.INSTANCE.d(TAG, "站点信息不可用 - 缓存为空");
                return SiteInfoResult.error("站点信息不存在");
            }
            
            if (!validator.validateSiteInfo(siteInfo)) {
                XLogUtil.INSTANCE.d(TAG, "站点信息不可用 - 数据无效");
                return SiteInfoResult.error("站点信息数据无效");
            }
            
            if (!cacheManager.isCacheValid()) {
                XLogUtil.INSTANCE.d(TAG, "站点信息已过期，建议刷新");
                return SiteInfoResult.success(siteInfo, SiteInfoSource.EXPIRED_CACHE);
            }
            
            XLogUtil.INSTANCE.d(TAG, "站点信息可用 - 站点ID: " + siteInfo.getSiteId());
            return SiteInfoResult.success(siteInfo, SiteInfoSource.CACHE);
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "检查站点信息可用性失败: " + e.getMessage());
            return SiteInfoResult.error("检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 站点信息结果类
     */
    public static class SiteInfoResult {
        private final boolean success;
        private final SiteInfoBean siteInfo;
        private final SiteInfoSource source;
        private final String errorMessage;
        
        private SiteInfoResult(boolean success, SiteInfoBean siteInfo, SiteInfoSource source, String errorMessage) {
            this.success = success;
            this.siteInfo = siteInfo;
            this.source = source;
            this.errorMessage = errorMessage;
        }
        
        public static SiteInfoResult success(SiteInfoBean siteInfo, SiteInfoSource source) {
            return new SiteInfoResult(true, siteInfo, source, null);
        }
        
        public static SiteInfoResult error(String errorMessage) {
            return new SiteInfoResult(false, null, null, errorMessage);
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public SiteInfoBean getSiteInfo() {
            return siteInfo;
        }
        
        public SiteInfoSource getSource() {
            return source;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        @Override
        public String toString() {
            if (success) {
                return String.format("SiteInfoResult{success=true, source=%s, siteId=%s}", 
                    source, siteInfo != null ? siteInfo.getSiteId() : "null");
            } else {
                return String.format("SiteInfoResult{success=false, error=%s}", errorMessage);
            }
        }
    }
    
    /**
     * 站点信息来源枚举
     */
    public enum SiteInfoSource {
        NETWORK("网络"),
        CACHE("缓存"),
        EXPIRED_CACHE("过期缓存"),
        FALLBACK_CACHE("降级缓存");
        
        private final String description;
        
        SiteInfoSource(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
        
        @Override
        public String toString() {
            return description;
        }
    }
}
