package dji.sampleV5.aircraft.mvvm.manager;

import android.text.TextUtils;

import java.util.List;

import dji.sampleV5.aircraft.mvvm.net.response.SiteInfoBean;
import dji.sampleV5.aircraft.mvvm.net.response.SiteListBean;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;

/**
 * 站点信息验证器
 * 负责验证站点信息的有效性和完整性
 * 
 * <AUTHOR> (Engineer)
 * @version 1.0
 * @date 2025-01-25
 */
public class SiteInfoValidator {
    
    private static final String TAG = "SiteInfoValidator";
    
    // 单例实例
    private static volatile SiteInfoValidator instance;
    
    private SiteInfoValidator() {
    }
    
    /**
     * 获取单例实例
     */
    public static SiteInfoValidator getInstance() {
        if (instance == null) {
            synchronized (SiteInfoValidator.class) {
                if (instance == null) {
                    instance = new SiteInfoValidator();
                }
            }
        }
        return instance;
    }
    
    /**
     * 验证站点信息的有效性
     * @param siteInfo 站点信息
     * @return true表示有效，false表示无效
     */
    public boolean validateSiteInfo(SiteInfoBean siteInfo) {
        if (siteInfo == null) {
            XLogUtil.INSTANCE.w(TAG, "站点信息验证失败 - 对象为空");
            return false;
        }
        
        // 检查站点ID
        if (TextUtils.isEmpty(siteInfo.getSiteId())) {
            XLogUtil.INSTANCE.w(TAG, "站点信息验证失败 - 站点ID为空");
            return false;
        }
        
        // 检查站点名称
        if (TextUtils.isEmpty(siteInfo.getSiteName())) {
            XLogUtil.INSTANCE.w(TAG, "站点信息验证失败 - 站点名称为空");
            return false;
        }
        
        // 检查站点位置
        if (siteInfo.getSiteLocation() == null || siteInfo.getSiteLocation().isEmpty()) {
            XLogUtil.INSTANCE.w(TAG, "站点信息验证失败 - 站点位置为空");
            return false;
        }
        
        // 检查位置坐标的有效性
        List<Double> location = siteInfo.getSiteLocation();
        if (location.size() < 2) {
            XLogUtil.INSTANCE.w(TAG, "站点信息验证失败 - 位置坐标不完整");
            return false;
        }
        
        Double longitude = location.get(0);
        Double latitude = location.get(1);
        
        if (longitude == null || latitude == null) {
            XLogUtil.INSTANCE.w(TAG, "站点信息验证失败 - 经纬度为空");
            return false;
        }
        
        // 检查经纬度范围的合理性
        if (!isValidLongitude(longitude) || !isValidLatitude(latitude)) {
            XLogUtil.INSTANCE.w(TAG, String.format("站点信息验证失败 - 经纬度超出有效范围: [%.6f, %.6f]", longitude, latitude));
            return false;
        }
        
        // 检查站点模式
        if (siteInfo.getSiteMode() == null || siteInfo.getSiteMode() < 0) {
            XLogUtil.INSTANCE.w(TAG, "站点信息验证失败 - 站点模式无效");
            return false;
        }
        
        XLogUtil.INSTANCE.d(TAG, String.format("站点信息验证成功 - 站点ID: %s, 名称: %s, 位置: [%.6f, %.6f]", 
            siteInfo.getSiteId(), siteInfo.getSiteName(), longitude, latitude));
        
        return true;
    }
    
    /**
     * 验证站点列表的有效性
     * @param siteList 站点列表
     * @return true表示有效，false表示无效
     */
    public boolean validateSiteList(List<SiteListBean.SiteItem> siteList) {
        if (siteList == null || siteList.isEmpty()) {
            XLogUtil.INSTANCE.w(TAG, "站点列表验证失败 - 列表为空");
            return false;
        }
        
        int validCount = 0;
        for (SiteListBean.SiteItem siteItem : siteList) {
            if (validateSiteItem(siteItem)) {
                validCount++;
            }
        }
        
        // 至少要有一个有效的站点
        boolean isValid = validCount > 0;
        
        if (isValid) {
            XLogUtil.INSTANCE.d(TAG, String.format("站点列表验证成功 - 总数: %d, 有效数: %d", siteList.size(), validCount));
        } else {
            XLogUtil.INSTANCE.w(TAG, String.format("站点列表验证失败 - 总数: %d, 有效数: %d", siteList.size(), validCount));
        }
        
        return isValid;
    }
    
    /**
     * 验证单个站点项的有效性
     * @param siteItem 站点项
     * @return true表示有效，false表示无效
     */
    public boolean validateSiteItem(SiteListBean.SiteItem siteItem) {
        if (siteItem == null) {
            return false;
        }
        
        // 检查站点ID
        if (TextUtils.isEmpty(siteItem.getSiteId())) {
            return false;
        }
        
        // 检查站点名称
        if (TextUtils.isEmpty(siteItem.getSiteName())) {
            return false;
        }
        
        // 检查站点位置
        if (siteItem.getSiteLocation() == null || siteItem.getSiteLocation().isEmpty()) {
            return false;
        }
        
        // 检查位置坐标
        List<Double> location = siteItem.getSiteLocation();
        if (location.size() < 2) {
            return false;
        }
        
        Double longitude = location.get(0);
        Double latitude = location.get(1);
        
        if (longitude == null || latitude == null) {
            return false;
        }
        
        return isValidLongitude(longitude) && isValidLatitude(latitude);
    }
    
    /**
     * 修复和清理站点信息
     * @param siteInfo 原始站点信息
     * @return 修复后的站点信息
     */
    public SiteInfoBean sanitizeSiteInfo(SiteInfoBean siteInfo) {
        if (siteInfo == null) {
            XLogUtil.INSTANCE.w(TAG, "无法修复空的站点信息");
            return null;
        }
        
        try {
            // 创建新的站点信息对象，避免修改原对象
            SiteInfoBean sanitized = new SiteInfoBean();
            
            // 修复站点ID
            sanitized.setSiteId(TextUtils.isEmpty(siteInfo.getSiteId()) ? "UNKNOWN_SITE" : siteInfo.getSiteId());
            
            // 修复站点名称
            sanitized.setSiteName(TextUtils.isEmpty(siteInfo.getSiteName()) ? "未知站点" : siteInfo.getSiteName());
            
            // 修复站点位置
            List<Double> location = siteInfo.getSiteLocation();
            if (location == null || location.isEmpty() || location.size() < 2) {
                // 使用默认位置（北京天安门）
                sanitized.setSiteLocation(List.of(116.397128, 39.916527));
                XLogUtil.INSTANCE.w(TAG, "站点位置无效，使用默认位置");
            } else {
                Double longitude = location.get(0);
                Double latitude = location.get(1);
                
                // 修复无效的经纬度
                if (longitude == null || !isValidLongitude(longitude)) {
                    longitude = 116.397128; // 默认经度
                    XLogUtil.INSTANCE.w(TAG, "经度无效，使用默认值");
                }
                
                if (latitude == null || !isValidLatitude(latitude)) {
                    latitude = 39.916527; // 默认纬度
                    XLogUtil.INSTANCE.w(TAG, "纬度无效，使用默认值");
                }
                
                sanitized.setSiteLocation(List.of(longitude, latitude));
            }
            
            // 修复站点模式
            sanitized.setSiteMode(siteInfo.getSiteMode() != null && siteInfo.getSiteMode() >= 0 ? siteInfo.getSiteMode() : 1);
            
            // 复制其他字段
            sanitized.setSiteAddress(siteInfo.getSiteAddress());
            sanitized.setSiteAltitude(siteInfo.getSiteAltitude());
            sanitized.setSiteEllipsAltitude(siteInfo.getSiteEllipsAltitude());
            sanitized.setSiteOptionLocation(siteInfo.getSiteOptionLocation());
            sanitized.setSiteOptionAltitude(siteInfo.getSiteOptionAltitude());
            sanitized.setSiteFlvUrl(siteInfo.getSiteFlvUrl());
            sanitized.setUavInfo(siteInfo.getUavInfo());
            sanitized.setHiveInfo(siteInfo.getHiveInfo());
            
            XLogUtil.INSTANCE.d(TAG, "站点信息修复完成 - 站点ID: " + sanitized.getSiteId());
            
            return sanitized;
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "修复站点信息失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查经度是否有效
     * @param longitude 经度
     * @return true表示有效，false表示无效
     */
    private boolean isValidLongitude(Double longitude) {
        return longitude != null && longitude >= -180.0 && longitude <= 180.0;
    }
    
    /**
     * 检查纬度是否有效
     * @param latitude 纬度
     * @return true表示有效，false表示无效
     */
    private boolean isValidLatitude(Double latitude) {
        return latitude != null && latitude >= -90.0 && latitude <= 90.0;
    }
    
    /**
     * 获取站点信息的摘要描述
     * @param siteInfo 站点信息
     * @return 摘要描述
     */
    public String getSiteInfoSummary(SiteInfoBean siteInfo) {
        if (siteInfo == null) {
            return "站点信息为空";
        }
        
        try {
            StringBuilder summary = new StringBuilder();
            summary.append("站点ID: ").append(siteInfo.getSiteId() != null ? siteInfo.getSiteId() : "未知");
            summary.append(", 名称: ").append(siteInfo.getSiteName() != null ? siteInfo.getSiteName() : "未知");
            
            if (siteInfo.getSiteLocation() != null && siteInfo.getSiteLocation().size() >= 2) {
                summary.append(String.format(", 位置: [%.6f, %.6f]", 
                    siteInfo.getSiteLocation().get(0), siteInfo.getSiteLocation().get(1)));
            } else {
                summary.append(", 位置: 未知");
            }
            
            summary.append(", 模式: ").append(siteInfo.getSiteMode() != null ? siteInfo.getSiteMode() : "未知");
            summary.append(", 有效性: ").append(validateSiteInfo(siteInfo) ? "有效" : "无效");
            
            return summary.toString();
            
        } catch (Exception e) {
            return "获取站点信息摘要失败: " + e.getMessage();
        }
    }
}
