package dji.sampleV5.aircraft.mqtt;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;

import com.google.gson.Gson;

import org.greenrobot.eventbus.Subscribe;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.event.DeviceConnectionEvent;
import dji.sampleV5.aircraft.event.Event;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.MissionJson;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.page.picture.PictureActivity;

public class MQttManager implements MQClient.MQttListener {
    private static final MQttManager INSTANCE = new MQttManager();
    private static final String TAG = "MQTTClient-MQTTManager";

    private MQClient mMqtt;
    private boolean isInitialized = false;
    private boolean isConnecting = false; // 添加连接状态标记
    private String currentSN = null; // 添加当前SN记录
    private static final int MAX_RETRY_COUNT = 3; // 最大重试次数
    private int retryCount = 0; // 当前重试次数

    private static final Object mConnectLock = new Object();

    private MQttManager() {}

    public static MQttManager getInstance() {
        return INSTANCE;
    }

    public void init(Context context) {
        if (isInitialized) {
            XLogUtil.INSTANCE.d(TAG, "MQTT管理器已初始化，跳过初始化");
            return;
        }
        
        XLogUtil.INSTANCE.d(TAG, "开始初始化MQTT管理器");
        isInitialized = true;
        /*mRemoteDataParser = new RemoteDataParser();
        mUploadTask = new UploadTask();
        mRetryTask = new RetryUploadTask();*/

        mMqtt = new MQClient(context);
        mMqtt.setMQttListener(this);
        XLogUtil.INSTANCE.d(TAG, "MQTT管理器初始化完成");
    }

    public void notifyAppStarted() {
        XLogUtil.INSTANCE.d(TAG, "应用启动，注册事件监听");
        Event.register(this);
    }

    public void notifyAppClosed() {
        XLogUtil.INSTANCE.d(TAG, "应用关闭，注销事件监听");
        Event.unregister(this);
        closeMQtt();
    }

    /**
     * 请求始终来自主线程
     */
/*    @Subscribe(sticky = true)
    public void onDeviceBindStatusEvent(DeviceBindStatusEvent event) {
        if (event.getRegisterStatus() == DeviceBindStatusEvent.STATUS_REGISTERED) {
            Device.DevicesBean devicesBean = event.getDevicesBean();
            if (devicesBean != null) {
                //Log.e(TAG,"onDeviceBindStatusEvent  devicesBean.getDid() = "+devicesBean.getDid()+"   devicesBean.getKey() = "+devicesBean.getKey());
                onDeviceKeyChanged(devicesBean.getDid(), devicesBean.getKey());
            }
        }
    }*/
    @Subscribe
    public void onDeviceConnectionEvent(DeviceConnectionEvent event) {
        XLogUtil.INSTANCE.d(TAG, "设备连接状态变化: " + event.isConnected());
        if (!event.isConnected()) {
            XLogUtil.INSTANCE.d(TAG, "设备断开连接，关闭MQTT");
            closeMQtt();
        }
    }

    public boolean isConnected() {
        boolean connected = mMqtt != null && mMqtt.isConnected();
        XLogUtil.INSTANCE.d(TAG, "MQTT连接状态检查: " + connected);
        return connected;
    }

    public void closeMQtt() {
        XLogUtil.INSTANCE.d(TAG, "关闭MQTT连接");
        isConnecting = false;
        onDisconnected();
        reset();
    }

    public void reset() {
        synchronized (mConnectLock) {
            XLogUtil.INSTANCE.d(TAG, "重置MQTT管理器");
            isConnecting = false;
            if (mMqtt != null) {
                XLogUtil.INSTANCE.d(TAG, "重置MQTT客户端");
                mMqtt.reset();
            } else {
                XLogUtil.INSTANCE.d(TAG, "MQTT客户端为空，无需重置");
            }
        }
    }

    /**
     * 拦截重复请求
     */
    private void onDeviceKeyChanged(String did, String key) {
        if (did != null && key != null) {
            /*if (!did.equals(this.mCurrentDid)) {
                onDisconnected();
                reset();
                connectMQtt(did, key);
            }*/
        }
    }

    /**
     * 检查是否应该连接MQ
     */
    public boolean shouldConnect() {
        LoginCache loginCache = SpUtil.getLoginCache();
        boolean shouldConnect = loginCache != null && !isConnected();
        XLogUtil.INSTANCE.d(TAG, "检查是否应该连接MQ: " + shouldConnect +
                           " (登录状态: " + (loginCache != null) + ", 连接状态: " + isConnected() + ")");
        return shouldConnect;
    }

    /**
     * 安全连接方法（带前置检查）
     */
    public void safeConnectMQtt(String sn) {
        if (!shouldConnect()) {
            XLogUtil.INSTANCE.d(TAG, "不满足连接条件，跳过MQ连接");
            return;
        }
        connectMQtt(sn);
    }

    /**
     * 用户登录时的连接方法
     */
    public void onUserLogin(String sn) {
        XLogUtil.INSTANCE.d(TAG, "用户登录，准备连接MQ，SN: " + sn);
        currentSN = sn;
        retryCount = 0; // 重置重试次数
        if (sn != null && !sn.isEmpty()) {
            safeConnectMQtt(sn);
        }
    }

    /**
     * 强制连接方法（用于注册成功后的连接）
     * 会重置连接状态并强制尝试连接
     */
    public void forceConnectMQtt(String sn) {
        XLogUtil.INSTANCE.d(TAG, "强制连接MQ，SN: " + sn);

        if (sn == null || sn.isEmpty()) {
            XLogUtil.INSTANCE.e(TAG, "强制连接失败：SN为空");
            return;
        }

        synchronized (mConnectLock) {
            // 如果正在连接中，先重置状态
            if (isConnecting) {
                XLogUtil.INSTANCE.d(TAG, "检测到正在连接中，重置连接状态后强制连接");
                isConnecting = false;
            }

            // 如果已连接但SN不同，先断开
            if (isConnected() && !sn.equals(currentSN)) {
                XLogUtil.INSTANCE.d(TAG, "SN不同，先断开现有连接，当前SN: " + currentSN + ", 新SN: " + sn);
                closeMQtt();
                // 等待断开完成
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            currentSN = sn;
            retryCount = 0;

            // 强制连接
            connectMQtt(sn);
        }
    }

    /**
     * 用户登出时的清理方法
     */
    public void onUserLogout() {
        XLogUtil.INSTANCE.d(TAG, "用户登出，关闭MQ连接");
        closeMQtt();
        // 清理相关状态
        isConnecting = false;
        currentSN = null;
        retryCount = 0;
    }

    public void connectMQtt(String sn) {
        if (sn == null || sn.isEmpty()) {
            XLogUtil.INSTANCE.e(TAG, "无效的SN，无法连接MQTT");
            return;
        }

        if (mMqtt == null) {
            XLogUtil.INSTANCE.e(TAG, "MQTT客户端未初始化");
            return;
        }

        synchronized (mConnectLock) {
            // 检查是否已连接或正在连接
            boolean connected = mMqtt.isConnected();
            if (connected) {
                XLogUtil.INSTANCE.d(TAG, "MQTT已连接，跳过连接请求");
                return;
            }

            if (isConnecting) {
                XLogUtil.INSTANCE.d(TAG, "MQTT正在连接中，跳过重复请求");
                return;
            }

            isConnecting = true;
            currentSN = sn; // 记录当前SN
            XLogUtil.INSTANCE.d(TAG, "开始连接MQTT，SN: " + sn);

            try {
                mMqtt.init(sn);
                XLogUtil.INSTANCE.d(TAG, "MQTT初始化请求已发送");
            } catch (Exception e) {
                XLogUtil.INSTANCE.e(TAG, "MQTT初始化失败: " + e.getMessage() +  ": " +  e);
                isConnecting = false;
            }
        }
    }

   /* public void publishDroneInfo(byte[] data, YNListener0 listener) {
        mMqtt.publish(mTopicUploadDroneInfo, data, listener);
    }*/

    @Override
    public void onConnected() {
        XLogUtil.INSTANCE.d(TAG, "MQTT连接成功回调");
        isConnecting = false;
        
        // 记录连接成功的详细信息
        XLogUtil.INSTANCE.d(TAG, "=== MQTT连接成功 ===");
        XLogUtil.INSTANCE.d(TAG, "连接时间: " + System.currentTimeMillis());
        XLogUtil.INSTANCE.d(TAG, "连接状态: " + (mMqtt != null ? mMqtt.isConnected() : "客户端为空"));
        
       /* mMqtt.publish(mNormalTopic, DEVICE_ON_MSG, null);
        mMqtt.subscribe(new String[]{mControlTopic, mWarnTopic}, null);*/
        log("publish device on");
       /* mUploadTask.start();
        mRemoteDataParser.onConnected();*/
    }

    @Override
    public void onDisconnected() {
        XLogUtil.INSTANCE.d(TAG, "MQTT断开连接回调");
        isConnecting = false;

        // 记录断开连接的详细信息
        XLogUtil.INSTANCE.d(TAG, "=== MQTT连接断开 ===");
        XLogUtil.INSTANCE.d(TAG, "断开时间: " + System.currentTimeMillis());

        // 检查是否是意外断开（用户仍在登录状态）
        if (shouldConnect() && currentSN != null) {
            XLogUtil.INSTANCE.d(TAG, "检测到意外断开，准备重连");
            // 延迟3秒后尝试重连
            ContextUtil.getHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (shouldConnect() && currentSN != null) {
                        XLogUtil.INSTANCE.d(TAG, "执行意外断开重连，SN: " + currentSN);
                        connectMQtt(currentSN);
                    }
                }
            }, 3000);
        }

       /* mUploadTask.reset();
        mRemoteDataParser.onDisconnected();*/
    }

    @Override
    public void onAuthenticationFailed() {
        XLogUtil.INSTANCE.e(TAG, "MQTT认证失败");
        isConnecting = false;

        // 记录认证失败的详细信息
        XLogUtil.INSTANCE.e(TAG, "=== MQTT认证失败 ===");
        XLogUtil.INSTANCE.e(TAG, "失败时间: " + System.currentTimeMillis());
        XLogUtil.INSTANCE.e(TAG, "服务器地址: " + (mMqtt != null ? "已初始化" : "未初始化"));
        XLogUtil.INSTANCE.e(TAG, "当前重试次数: " + retryCount);

        // 实现重试机制
        if (retryCount < MAX_RETRY_COUNT && shouldConnect() && currentSN != null) {
            retryCount++;
            XLogUtil.INSTANCE.d(TAG, "尝试重连，第" + retryCount + "次");

            // 延迟重连，递增延迟时间
            ContextUtil.getHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (shouldConnect() && currentSN != null) {
                        XLogUtil.INSTANCE.d(TAG, "执行重连，SN: " + currentSN);
                        connectMQtt(currentSN);
                    }
                }
            }, 5000 * retryCount); // 5秒、10秒、15秒递增延迟
        } else {
            XLogUtil.INSTANCE.e(TAG, "重连次数超限或不满足重连条件，停止重连");
            reset();
            onDisconnected();
        }
    }

    @Override
    public void onPreRelease() {
        XLogUtil.INSTANCE.d(TAG, "MQTT预释放回调");
       /* mMqtt.unsubscribe(new String[]{mControlTopic, mWarnTopic});
        mMqtt.publish(mNormalTopic, DEVICE_OFF_MSG, null);*/
        log("publish device off");
    }

    Activity activity;
    @Override
    public void onGetMsg(String topic, String message) {
        XLogUtil.INSTANCE.d(TAG, "=== 收到MQTT消息 ===");
        XLogUtil.INSTANCE.d(TAG, "主题: " + topic);
        XLogUtil.INSTANCE.d(TAG, "内容: " + message);
        XLogUtil.INSTANCE.d(TAG, "时间: " + System.currentTimeMillis());
        
        try {
            // 验证消息格式
            if (message == null || message.trim().isEmpty()) {
                XLogUtil.INSTANCE.e(TAG, "消息内容为空，跳过处理");
                return;
            }
            
            XLogUtil.INSTANCE.d(TAG, "开始解析JSON消息...");
            MissionJson missionJson = new Gson().fromJson(message, MissionJson.class);
            if (missionJson == null) {
                XLogUtil.INSTANCE.e(TAG, "消息解析失败：无法转换为MissionJson对象");
                XLogUtil.INSTANCE.e(TAG, "原始消息内容: " + message);
                return;
            }
            
            XLogUtil.INSTANCE.d(TAG, "消息解析成功，任务信息: " + missionJson);
            
            // 获取当前活动
            activity = ContextUtil.getCurrentActivity();
            XLogUtil.INSTANCE.d(TAG, "当前活动: " + (activity != null ? activity.getClass().getSimpleName() : "null"));
            
            if (activity instanceof DefaultLayoutActivity) {
                XLogUtil.INSTANCE.d(TAG, "当前在DefaultLayoutActivity，直接发送事件");
                Event.post(missionJson);
                XLogUtil.INSTANCE.d(TAG, "事件已发送到EventBus");
            } else {
                XLogUtil.INSTANCE.d(TAG, "当前不在DefaultLayoutActivity，显示确认对话框");
                if (activity == null) {
                    XLogUtil.INSTANCE.e(TAG, "当前活动为空，无法显示对话框");
                    return;
                }
                
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        XLogUtil.INSTANCE.d(TAG, "在UI线程显示确认对话框");
                        new AlertDialog.Builder(activity)
                                .setMessage("接收到任务指令，是否去执行")
                                .setNegativeButton(R.string.dialog_cancel, (dialog, which) -> {
                                    XLogUtil.INSTANCE.d(TAG, "用户取消执行任务");
                                })
                                .setPositiveButton(R.string.dialog_ok, (dialog, which) -> {
                                    XLogUtil.INSTANCE.d(TAG, "用户确认执行任务，开始处理...");
                                    
                                    if (activity instanceof PictureActivity){
                                        XLogUtil.INSTANCE.d(TAG, "关闭PictureActivity");
                                        activity.finish();
                                    }
                                    
                                    // 延迟启动DefaultLayoutActivity
                                    ContextUtil.getHandler().postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            XLogUtil.INSTANCE.d(TAG, "延迟1秒后启动DefaultLayoutActivity");
                                            activity = ContextUtil.getCurrentActivity();
                                            if (activity != null) {
                                                activity.startActivity(new Intent(activity, DefaultLayoutActivity.class));
                                                XLogUtil.INSTANCE.d(TAG, "DefaultLayoutActivity启动请求已发送");
                                            } else {
                                                XLogUtil.INSTANCE.e(TAG, "当前活动为空，无法启动DefaultLayoutActivity");
                                            }
                                        }
                                    }, 1000);
                                    
                                    // 延迟发送任务事件
                                    ContextUtil.getHandler().postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            XLogUtil.INSTANCE.d(TAG, "延迟3.5秒后发送任务事件");
                                            Event.post(missionJson);
                                            XLogUtil.INSTANCE.d(TAG, "任务事件已发送到EventBus");
                                        }
                                    }, 3500);
                                }).create().show();
                    }
                });
            }
            
            XLogUtil.INSTANCE.d(TAG, "消息处理完成");
            
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "处理MQTT消息时发生异常: " + e.getMessage() +  ": " +  e);
            XLogUtil.INSTANCE.e(TAG, "异常堆栈信息已记录");
        }
       /* if (topic.equals(mControlTopic)) {
            mRemoteDataParser.parse(this.mCurrentDid, msg);
        }else if (topic.equals(mWarnTopic)){
            try {
                JSONObject jsonObject = new JSONObject(new String(msg));
                String jsonType = jsonObject.getString("type");
                if (jsonType.equals("collision")){
                    Event.post(new Events.FlyWarning(Events.EventsType.COLLISION, new String(msg)));
                }else if (jsonType.equals("nofly")){
                    Event.post(new Events.FlyWarning(Events.EventsType.NO_FLY, new String(msg)));
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }*/
    }

    @Override
    public void onMsgDelivered() {
        XLogUtil.INSTANCE.d(TAG, "MQTT消息发送完成");
    }

    @Override
    public void onRelease() {
        XLogUtil.INSTANCE.d(TAG, "MQTT释放完成");
    }

    private void log(String msg) {
        XLogUtil.INSTANCE.d(TAG, "log: " + msg);
    }
    
    // 添加获取MQTT状态的方法
    public String getMQTTStatus() {
        if (mMqtt == null) {
            return "MQTT客户端未初始化";
        }
        return mMqtt.getSubscriptionStatus();
    }
    
    // 添加手动重试订阅的方法
    public void retrySubscription() {
        if (mMqtt != null) {
            XLogUtil.INSTANCE.d(TAG, "手动重试MQTT订阅");
            mMqtt.retrySubscription();
        } else {
            XLogUtil.INSTANCE.e(TAG, "MQTT客户端为空，无法重试订阅");
        }
    }

    // 添加连接状态检查方法
    public boolean isConnecting() {
        return isConnecting;
    }
    
    // 添加获取详细连接状态的方法
    public String getConnectionStatus() {
        StringBuilder status = new StringBuilder();
        status.append("MQTT管理器状态:\n");
        status.append("- 已初始化: ").append(isInitialized).append("\n");
        status.append("- 正在连接: ").append(isConnecting).append("\n");
        status.append("- 客户端存在: ").append(mMqtt != null).append("\n");
        
        if (mMqtt != null) {
            status.append("- 客户端连接: ").append(mMqtt.isConnected()).append("\n");
            status.append("\n").append(mMqtt.getSubscriptionStatus());
        }
        
        return status.toString();
    }
}
